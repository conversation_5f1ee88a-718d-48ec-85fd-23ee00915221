// Global polyfills for server-side rendering compatibility
if (typeof window === 'undefined') {
  global.window = {
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
    location: { href: '', hostname: '', protocol: 'https:', pathname: '/' },
    history: { pushState: () => {}, replaceState: () => {} },
    navigator: {
      userAgent: 'Node.js',
      language: 'en',
      platform: 'Node.js'
    },
    innerWidth: 1024,
    innerHeight: 768,
    screen: { width: 1024, height: 768 },
    localStorage: {
      getItem: () => null,
      setItem: () => {},
      removeItem: () => {},
      clear: () => {}
    },
    sessionStorage: {
      getItem: () => null,
      setItem: () => {},
      removeItem: () => {},
      clear: () => {}
    },
    getComputedStyle: () => ({}),
    requestAnimationFrame: (cb) => setTimeout(cb, 16),
    cancelAnimationFrame: (id) => clearTimeout(id),
    matchMedia: () => ({ matches: false, addListener: () => {}, removeListener: () => {} }),
    scroll: () => {},
    scrollTo: () => {},
    open: () => null,
    close: () => {},
    focus: () => {}
  };
}

if (typeof document === 'undefined') {
  global.document = {
    createElement: (tag) => ({
      tagName: tag.toUpperCase(),
      children: [],
      style: {},
      setAttribute: () => {},
      getAttribute: () => null,
      hasAttribute: () => false,
      removeAttribute: () => {},
      classList: {
        add: () => {},
        remove: () => {},
        contains: () => false,
        toggle: () => false
      },
      addEventListener: () => {},
      removeEventListener: () => {},
      appendChild: function(child) {
        this.children.push(child);
        return child;
      },
      removeChild: function(child) {
        const index = this.children.indexOf(child);
        if (index > -1) this.children.splice(index, 1);
        return child;
      },
      innerHTML: '',
      textContent: '',
      value: '',
      checked: false,
      focus: () => {},
      blur: () => {},
      click: () => {},
      cloneNode: () => global.document.createElement(tag)
    }),
    querySelector: () => null,
    querySelectorAll: () => [],
    getElementById: () => null,
    getElementsByClassName: () => [],
    getElementsByTagName: () => [],
    body: null,
    head: null,
    documentElement: null,
    cookie: '',
    title: '',
    readyState: 'complete',
    addEventListener: () => {},
    removeEventListener: () => {},
    createEvent: () => ({
      initEvent: () => {},
      preventDefault: () => {},
      stopPropagation: () => {}
    }),
    createTextNode: (text) => ({ textContent: text, nodeValue: text }),
    createDocumentFragment: () => ({ appendChild: () => {}, children: [] })
  };
  
  // Create basic body and head elements
  global.document.body = global.document.createElement('body');
  global.document.head = global.document.createElement('head');
  global.document.documentElement = global.document.createElement('html');
  global.document.documentElement.appendChild(global.document.head);
  global.document.documentElement.appendChild(global.document.body);
}

if (typeof self === 'undefined') {
  global.self = global.window || global;
}

if (typeof navigator === 'undefined') {
  global.navigator = {
    userAgent: 'Node.js',
    language: 'en',
    languages: ['en'],
    platform: 'Node.js',
    cookieEnabled: false,
    onLine: true,
    geolocation: null
  };
}

// Additional globals that might be needed
if (typeof XMLHttpRequest === 'undefined') {
  global.XMLHttpRequest = function() {
    return {
      open: () => {},
      send: () => {},
      setRequestHeader: () => {},
      addEventListener: () => {},
      removeEventListener: () => {},
      readyState: 4,
      status: 200,
      responseText: '',
      response: ''
    };
  };
}
